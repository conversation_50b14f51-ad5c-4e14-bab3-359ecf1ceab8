import React, { useEffect, useRef, useState } from "react";
import { <PERSON>, Button } from "antd";
import { FormattedMessage } from "umi";
import ChatMessage from "../chatMessage";
import ChatInput from "../chatInput";
import styles from "./index.module.less";

const ChatArea = ({
  messages,
  onSendMessage,
  onRegenerate,
  onPause,
  isLoading = false,
  isGenerating = false,
  onLoadMoreMessages,
  isLoadingMoreMessages = false,
  hasMoreMessages = true,
  activeConversationId,
  onLike,
  onDislike,
}) => {
  const messagesContainerRef = useRef(null);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [previousScrollHeight, setPreviousScrollHeight] = useState(0);
  const [previousMessageCount, setPreviousMessageCount] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const isScrollingManuallyRef = useRef(false);
  const [showingCustomButton, setShowingCustomButton] = useState(false);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);

  const handleCopy = (content) => {
    navigator.clipboard.writeText(content);
  };

  const handleLike = (messageId) => {
    onLike?.(messageId);
  };

  const handleDislike = (messageId) => {
    onDislike?.(messageId);
  };

  const handleRegenerate = (messageId) => {
    onRegenerate?.(messageId);
  };

  // 处理滚动到底部
  const handleScrollToBottom = () => {
    if (messagesContainerRef.current) {
      messagesContainerRef.current.scrollTo({
        top: messagesContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
      // 重置自动滚动状态
      setIsAutoScrollEnabled(true);
      isScrollingManuallyRef.current = false;
      setShowScrollToBottom(false);
    }
  };

  // 处理定制化按钮点击
  const handleCustomAction = (buttonText) => {
    if (onSendMessage && activeConversationId) {
      const customMessage = {
        chatId: activeConversationId,
        content: {
          userPrompt: buttonText,
        },
        // scene: "DEVELOPMENT_EMAIL" // 使用scene标识
      };
      onSendMessage(customMessage.content.userPrompt, customMessage);
    }
  };

  // 判断是否显示定制化按钮（基于tips字段）
  const shouldShowCustomButton = (message, messageIndex) => {
    // 必须是AI消息
    if (message.role !== "assistant") {
      return false;
    }

    // 必须是最后一条消息
    if (messageIndex !== messages.length - 1) {
      return false;
    }

    // AI必须已经完成输出（不在输出状态）
    if (message.isTyping || message.isThinking || isGenerating) {
      return false;
    }

    // 基于AI消息的tips字段判断
    // 如果AI回复有tips数组且不为空，显示按钮
    return (
      message.tips && Array.isArray(message.tips) && message.tips.length > 0
    );
  };

  // 添加滚动事件监听器
  useEffect(() => {
    const container = messagesContainerRef.current;
    if (!container || messages.length === 0) return;

    const scrollHandler = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;

      // 检查是否滚动到顶部（用于加载更多历史消息）
      if (
        scrollTop === 0 &&
        hasMoreMessages &&
        !isLoadingMoreMessages &&
        onLoadMoreMessages
      ) {
        // 保存当前滚动高度，用于加载完成后恢复位置
        setPreviousScrollHeight(scrollHeight);
        onLoadMoreMessages();
        return;
      }

      // 检查是否需要显示"回到底部"按钮
      // 只有当用户滚动距离底部超过200px时才显示按钮
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      setShowScrollToBottom(distanceFromBottom > 200);

      // 检测用户手动滚动行为（无论是否在生成中都要检测）
      const isAtBottom = scrollHeight - scrollTop - clientHeight < 50; // 50px容差

      if (!isScrollingManuallyRef.current) {
        // 如果当前是自动滚动状态，检测用户是否滚动离开底部
        if (!isAtBottom && isAutoScrollEnabled) {
          setIsAutoScrollEnabled(false);
          isScrollingManuallyRef.current = true;
        }
      } else {
        // 如果当前是手动滚动状态，检测用户是否滚动回到底部
        if (isAtBottom && !isAutoScrollEnabled) {
          setIsAutoScrollEnabled(true);
          isScrollingManuallyRef.current = false;
        }
      }
    };

    container.addEventListener("scroll", scrollHandler, { passive: true });

    return () => {
      container.removeEventListener("scroll", scrollHandler);
    };
  }, [
    messages.length,
    hasMoreMessages,
    isLoadingMoreMessages,
    onLoadMoreMessages,
  ]);

  // 首次加载消息时滚动到底部
  useEffect(() => {
    if (messages.length > 0 && isInitialLoad && messagesContainerRef.current) {
      // 延迟执行，确保DOM完全渲染
      setTimeout(() => {
        if (messagesContainerRef.current) {
          messagesContainerRef.current.scrollTop =
            messagesContainerRef.current.scrollHeight;
          setIsInitialLoad(false); // 标记为非初始加载
        }
      }, 100);
    }
  }, [messages.length, isInitialLoad]);

  // 当切换会话时重置初始加载状态
  useEffect(() => {
    if (messages.length === 0) {
      setIsInitialLoad(true);
      setPreviousMessageCount(0); // 重置消息计数
      setIsAutoScrollEnabled(true); // 重置自动滚动状态
      isScrollingManuallyRef.current = false;
      setShowScrollToBottom(false); // 重置回到底部按钮状态
    }
  }, [messages.length === 0]);

  // 加载更多消息后恢复滚动位置
  useEffect(() => {
    if (
      previousScrollHeight > 0 &&
      messagesContainerRef.current &&
      !isInitialLoad
    ) {
      const container = messagesContainerRef.current;
      const newScrollHeight = container.scrollHeight;
      const scrollDiff = newScrollHeight - previousScrollHeight;

      if (scrollDiff > 0) {
        // 恢复到加载前的相对位置，保持用户当前的阅读位置
        // 由于新消息被添加到顶部，需要调整滚动位置
        requestAnimationFrame(() => {
          container.scrollTop = scrollDiff;
          setPreviousScrollHeight(0); // 重置
        });
      }
    }
  }, [messages.length, previousScrollHeight, isInitialLoad]);

  // 检测新消息并滚动到底部
  useEffect(() => {
    const currentMessageCount = messages.length;

    // 如果消息数量增加了，且不是初始加载，且不是加载历史消息
    if (
      currentMessageCount > previousMessageCount &&
      !isInitialLoad &&
      previousScrollHeight === 0
    ) {
      // 检查最新消息（数组最后一个元素）是否是用户消息
      const latestMessage = messages[messages.length - 1];
      if (
        latestMessage &&
        latestMessage.role === "user" &&
        messagesContainerRef.current
      ) {
        // 用户发送新消息时，重置自动滚动状态并滚动到底部
        setIsAutoScrollEnabled(true);
        isScrollingManuallyRef.current = false;
        setShowScrollToBottom(false); // 隐藏回到底部按钮

        setTimeout(() => {
          if (messagesContainerRef.current) {
            messagesContainerRef.current.scrollTop =
              messagesContainerRef.current.scrollHeight;
          }
        }, 100);
      }
    }

    // 更新消息计数
    setPreviousMessageCount(currentMessageCount);
  }, [
    messages.length,
    previousMessageCount,
    isInitialLoad,
    previousScrollHeight,
  ]);

  // 监听AI生成状态变化
  useEffect(() => {
    if (isGenerating && !isInitialLoad) {
      // AI开始生成时，重置自动滚动状态并滚动到底部
      setIsAutoScrollEnabled(true);
      isScrollingManuallyRef.current = false;
      setShowScrollToBottom(false); // 隐藏回到底部按钮

      // 立即滚动到底部
      if (messagesContainerRef.current) {
        messagesContainerRef.current.scrollTop =
          messagesContainerRef.current.scrollHeight;
      }
    }
    // 当AI生成完成时，不做任何滚动操作，保持用户当前的滚动位置
  }, [isGenerating, isInitialLoad]);

  // AI输出过程中的自动滚动
  useEffect(() => {
    if (isGenerating && isAutoScrollEnabled && messagesContainerRef.current) {
      // AI正在输出且自动滚动启用时，持续滚动到底部
      const scrollToBottom = () => {
        if (messagesContainerRef.current && isAutoScrollEnabled) {
          messagesContainerRef.current.scrollTop =
            messagesContainerRef.current.scrollHeight;
        }
      };

      // 设置定时器持续滚动（因为内容在动态增加）
      const scrollInterval = setInterval(scrollToBottom, 100);

      return () => {
        clearInterval(scrollInterval);
      };
    }
  }, [isGenerating, isAutoScrollEnabled, messages]);

  // 检测按钮显示状态变化，但不自动滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      const shouldShow = shouldShowCustomButton(
        lastMessage,
        messages.length - 1
      );

      // 只更新按钮显示状态，不自动滚动
      // 保留用户当前的滚动位置
      setShowingCustomButton(shouldShow);
    }
  }, [messages, isGenerating, showingCustomButton]); // 依赖isGenerating确保AI输出完成后能检测到

  return (
    <div className={styles.chatArea}>
      {messages.length === 0 ? (
        // 初始状态 - 所有内容居中显示
        <div className={styles.centerLayout}>
          <div className={styles.centerContent}>
            <div className={styles.welcomeText}>
              <h1 className={styles.welcomeTitle}><FormattedMessage id="ai-welcome-greeting" /></h1>
              <div className={styles.welcomeSubtitle}>
                <FormattedMessage id="ai-welcome-description" />
              </div>
            </div>
            <div className={styles.centerInput}>
              <ChatInput
                onSendMessage={onSendMessage}
                onPause={onPause}
                disabled={isLoading}
                isGenerating={isGenerating}
              />
            </div>
          </div>
          <div className={styles.helpText}>
            <FormattedMessage id="help-text-disclaimer" />
          </div>
        </div>
      ) : (
        // 有消息时的布局
        <div className={styles.chatLayout}>
          <div className={styles.messagesContainer} ref={messagesContainerRef}>
            <div className={styles.messagesList}>
              {/* 历史消息加载指示器 */}
              {isLoadingMoreMessages && (
                <div className={styles.loadingMore}>
                  <Spin size="small" />
                </div>
              )}
              {messages.map((message, index) => (
                <ChatMessage
                  key={message.id}
                  message={message}
                  onCopy={handleCopy}
                  onLike={handleLike}
                  onDislike={handleDislike}
                  onRegenerate={handleRegenerate}
                  showCustomButton={shouldShowCustomButton(message, index)}
                  onCustomAction={handleCustomAction}
                />
              ))}
            </div>
          </div>
          <div className={styles.fixedBottom}>
            {/* 回到底部按钮 - 固定在输入组件中间上方 */}
            {showScrollToBottom && (
              <div className={styles.scrollToBottomBtn} onClick={handleScrollToBottom}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 40 40"
                  fill="none"
                >
                  <circle
                    cx="20"
                    cy="20"
                    r="19"
                    fill="white"
                    stroke="#E6E7EB"
                  />
                </svg>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M18 9L12 15L6 9"
                    stroke="#444558"
                    strokeWidth="1.8"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            )}
            <ChatInput
              onSendMessage={onSendMessage}
              onPause={onPause}
              disabled={isLoading}
              isGenerating={isGenerating}
            />
            <div className={styles.helpText}>
              <FormattedMessage id="help-text-disclaimer" />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatArea;
