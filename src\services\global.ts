import { request } from 'umi';
import urlConfig from './hostConfig'
const appEnv = process.env.UMI_APP_ENV || 'dev';

// 获取权限信息
export async function getAuth(params: any) {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].accountHost;
  return request(`${urlHost}/api/auth/user/info`, {
    method: 'GET',
  });
}

// 获取权限信息
export async function logout(params: any) {
  const urlHost = appEnv === 'dev' ? '' : urlConfig[appEnv].loginHost;
  return request(`${urlHost}/api/auth/token/logout`, {
    method: 'GET',
  });
}