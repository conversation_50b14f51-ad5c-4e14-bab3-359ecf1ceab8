/* ChatInput 样式 */
.inputContainer {
}

.inputForm {
  max-width: 1024px;
  margin: 0 auto;
}

.inputArea {
  cursor: text;
  height: 132px;
  padding: 12px;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #fff;
  border: 1px solid #d9d9d9;
  transition: all 0.2s ease;

  &:hover {
    border-color: #4096ff;
  }

  &.focused {
    border-color: #1677ff;
    box-shadow: 0px 0px 0px 4px #e3edff;
    outline: 0;
  }
  .fixedText {
    color: rgba(0, 0, 0, 0.65);
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;
    display: flex;
    align-content: center;
  }

  .companyText {
    cursor: pointer;
    display: flex;
    align-items: center;
    height: 26px;
    margin: 0 4px;
    padding: 0 4px;
    color: #6991ff;
    font-size: 16px;
    font-weight: 400;
    line-height: 26px;

    border-radius: 2px;
    background: var(---B1, #f0f6ff);

    .company {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .lf,
    .rf {
      position: relative;
      top: -2px;
    }

    .arrowIcon {
      margin-left: 4px;
    }
  }

  .mainButton {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;

    .pause {
      width: 12px;
      height: 12px;
      background: #fff;
      border-radius: 1.286px;
    }
  }
}

.companyDropdown {
  background: #fff;
  border-radius: 6px;
  box-shadow:
    0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  max-height: 369px;
  max-width: 600px;
  overflow: hidden;
  padding: 4px;

  // 自定义滚动条样式，与Sidebar组件保持一致
  :global(.infinite-scroll-component) {
    &::-webkit-scrollbar {
      width: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: var(---, rgba(0, 0, 0, 0.06));
      border-radius: 3px;
    }
  }

  .soure {
    border-top: 1px solid #e6e7eb;
    margin: 4px 8px 0 8px;
    .text {
      margin: 9px 4px 5px 4px;
      color: var(---, rgba(0, 0, 0, 0.45));
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }
  }
}

.companyItem {
  padding: 5px 12px;
  cursor: pointer;
  border-radius: 6px;

  &:hover {
    background: rgba(0, 0, 0, 0.04);
  }

  &:last-child {
    border-bottom: none;
  }
}

.companyName {
  display: flex;
  align-items: center;
  gap: 8px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  max-width: 580px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.companyInfo {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

.loadingMore {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  gap: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.emptyState {
  width: 320px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;

  // 自定义Empty组件的样式
  :global(.ant-empty) {
    .ant-empty-description {
      color: rgba(0, 0, 0, 0.45);
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
    }
  }
}
