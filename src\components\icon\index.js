const AIFilled = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="28"
    height="28"
    viewBox="0 0 28 28"
    fill="none"
    {...props}
  >
    <rect
      x="0.291667"
      y="0.291667"
      width="27.4167"
      height="27.4167"
      rx="13.7083"
      fill="#3D68F5"
      fillOpacity="0.05"
    />
    <rect
      x="0.291667"
      y="0.291667"
      width="27.4167"
      height="27.4167"
      rx="13.7083"
      stroke="#E8EBED"
      strokeWidth="0.583333"
    />
    <rect
      x="0.291667"
      y="0.291667"
      width="27.4167"
      height="27.4167"
      rx="13.7083"
      stroke="url(#paint0_linear_310_11649)"
      strokeOpacity="0.73"
      strokeWidth="0.583333"
    />
    <path
      d="M17.4229 20.334H14.1455L13.415 17.9365H9.24609L8.51855 20.334H5.25L9.88867 7.5H12.7578L17.4229 20.334ZM21.584 20.334H18.5488V7.5H21.584V20.334ZM9.97168 15.5479H12.6885L11.3271 11.0811L9.97168 15.5479Z"
      fill="url(#paint1_linear_310_11649)"
    />
    <defs>
      <linearGradient
        id="paint0_linear_310_11649"
        x1="0"
        y1="0"
        x2="31.4178"
        y2="5.16377"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#40A0FB" />
        <stop offset="0.495859" stopColor="#334BFD" />
        <stop offset="1" stopColor="#C259FF" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_310_11649"
        x1="5.25"
        y1="7.5"
        x2="17.7196"
        y2="23.3703"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#3D68F5" />
        <stop offset="1" stopColor="#9175F8" />
      </linearGradient>
    </defs>
  </svg>
);

const ArrowUpFilled = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M5.96357 8.81248L7.33378 10.3588C7.50088 10.5551 7.74526 10.6668 8.00322 10.6668C8.26118 10.6658 8.50452 10.553 8.67162 10.3557L12.4543 6.08943C12.8251 5.67074 12.6966 5.3335 12.1786 5.3335L3.82365 5.3335C3.30356 5.3335 3.17406 5.66657 3.54794 6.08839L5.96357 8.81248Z"
      fill="currentColor"
    />
  </svg>
);

const SendFilled = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M13.6208 2.37842L9.38297 14.4865L6.96134 9.03788L1.5127 6.61626L13.6208 2.37842Z"
      fill="white"
      stroke="white"
      strokeWidth="1.62162"
      strokeLinejoin="round"
    />
  </svg>
);

const ArrowUpOutlined = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M4.3335 10L8.3335 6L12.3335 10"
      stroke="black"
      strokeOpacity="0.45"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const ArrowDowmOutlined = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M4.3335 6L8.3335 10L12.3335 6"
      stroke="black"
      strokeOpacity="0.45"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const CopyIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_469_1335)">
      <path
        d="M11 -4.0625C11 -4.58027 11.4197 -5 11.9375 -5H20.0625C20.5803 -5 21 -4.58027 21 -4.0625V4.0625C21 4.58027 20.5803 5 20.0625 5"
        stroke="black"
        strokeOpacity="0.45"
        strokeWidth="1.33333"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="1.6665"
        y="4"
        width="10"
        height="10.4"
        rx="1.33333"
        stroke="black"
        strokeOpacity="0.45"
        strokeWidth="1.2"
        strokeLinejoin="round"
      />
      <path
        d="M13.6668 11.3335V11.3335C14.035 11.3335 14.3335 11.035 14.3335 10.6668V2.66683C14.3335 1.93045 13.7365 1.3335 13.0002 1.3335H5.06683C4.66182 1.3335 4.3335 1.66182 4.3335 2.06683V2.06683V2.1335"
        stroke="black"
        strokeOpacity="0.45"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_469_1335">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const RefreshIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M4.62744 9.92822C4.95866 9.9284 5.22705 10.1966 5.22705 10.5278C5.22696 10.859 4.95861 11.1273 4.62744 11.1274H3.24463C4.30987 12.592 6.00555 13.5386 7.95361 13.5386C10.269 13.5384 12.2746 12.2051 13.2407 10.2612C13.3882 9.96457 13.7477 9.84345 14.0444 9.99072C14.3409 10.1383 14.4622 10.4978 14.3149 10.7944C13.1539 13.1307 10.742 14.7386 7.95361 14.7388C5.77796 14.7388 3.86565 13.76 2.57861 12.2261V13.4165C2.57853 13.7478 2.31032 14.0171 1.979 14.0171C1.64784 14.0169 1.37948 13.7477 1.37939 13.4165V10.5278C1.37939 10.1966 1.64778 9.9284 1.979 9.92822C1.98216 9.92822 1.98562 9.92915 1.98877 9.9292L1.99951 9.92822H4.62744ZM8.00342 1.26123C10.1799 1.26138 12.0926 2.2401 13.3794 3.7749V2.5835C13.3794 2.25223 13.6478 1.98406 13.979 1.98389C14.3104 1.98389 14.5786 2.25213 14.5786 2.5835V5.47217C14.5785 5.80346 14.3103 6.07275 13.979 6.07275C13.9731 6.07275 13.9673 6.07097 13.9614 6.0708C13.9585 6.07084 13.9556 6.07178 13.9526 6.07178H11.3286L11.2075 6.06006C10.9343 6.00398 10.729 5.762 10.729 5.47217C10.7291 5.1824 10.9343 4.94033 11.2075 4.88428L11.3286 4.87256H12.7114C11.6463 3.40789 9.95136 2.46061 8.00342 2.46045C5.68794 2.46045 3.68256 3.79486 2.71631 5.73877C2.56887 6.03545 2.20833 6.1566 1.91162 6.00928C1.65214 5.8802 1.52762 5.58849 1.59912 5.31885L1.64111 5.20459L1.75439 4.98779C2.95419 2.7697 5.30223 1.26123 8.00342 1.26123Z"
      fill="black"
      fillOpacity="0.45"
    />
  </svg>
);

const BeFondOfOutlined = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_469_1352)">
      <path
        d="M7.33447 0.899902C8.74266 0.899902 9.88499 2.0416 9.88525 3.44971V5.44971H13.1851C13.7398 5.44545 14.2691 5.68445 14.6333 6.10303C14.9988 6.52349 15.1611 7.08341 15.0776 7.63428V7.63525L14.1812 13.4858C14.0389 14.4198 13.2318 15.1056 12.2876 15.0981V15.0991L4.73486 15.1001H2.99951V15.0991C1.93854 15.1125 1.03272 14.3333 0.890137 13.2808C0.886538 13.2542 0.885259 13.2266 0.885254 13.1997V8.6499C0.885259 8.62319 0.886579 8.5963 0.890137 8.56982C1.02714 7.55698 1.9 6.66564 2.99951 6.6792V6.67822H4.37744L6.78662 1.25537L6.82764 1.17822C6.9364 1.00658 7.1273 0.900147 7.33447 0.899902ZM2.98877 7.87842C2.57846 7.87135 2.16309 8.2237 2.08447 8.70264V13.145C2.15679 13.585 2.54043 13.9077 2.98877 13.8999H4.13525V7.87842H2.98877ZM5.33447 7.47607V13.8999H12.2944C12.6435 13.9038 12.9423 13.6493 12.9946 13.3042V13.3032L13.8921 7.45361C13.9224 7.25103 13.8624 7.04475 13.728 6.89014C13.5933 6.73546 13.397 6.64763 13.1919 6.6499H9.28467C8.95354 6.64962 8.68506 6.38051 8.68506 6.04932V3.44971C8.68484 2.83155 8.26873 2.31072 7.70166 2.15088L5.33447 7.47607Z"
        fill="black"
        fillOpacity="0.45"
      />
    </g>
    <defs>
      <clipPath id="clip0_469_1352">
        <rect
          width="16"
          height="16"
          fill="white"
          transform="matrix(-1 0 0 -1 16 16)"
        />
      </clipPath>
    </defs>
  </svg>
);

const BeFondOfFilled = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_411_5895)">
      <path
        d="M2.80469 6.67822C3.1311 6.68495 3.39247 6.95138 3.39258 7.27783V14.5005C3.39224 14.8267 3.13096 15.0934 2.80469 15.1001C1.77133 15.1212 1.01189 14.2547 0.897461 13.269C0.894811 13.2462 0.893561 13.2227 0.893555 13.1997V8.6499C0.893559 8.6269 0.894817 8.60342 0.897461 8.58057C1.0069 7.63865 1.73513 6.65637 2.80469 6.67822ZM7.34668 0.900879C8.75489 0.901055 9.89746 2.04246 9.89746 3.45068V5.45068H13.1973C13.7521 5.44652 14.2813 5.68524 14.6455 6.104C15.0109 6.52453 15.1734 7.08444 15.0898 7.63525V7.63623L14.1934 13.4868C14.0509 14.4205 13.2437 15.1064 12.2998 15.0991V15.1001H4.74707C4.41586 15.1001 4.14772 14.8316 4.14746 14.5005V7.3501C4.14752 7.26639 4.16526 7.18345 4.19922 7.10693L6.79883 1.25635L6.83984 1.1792C6.94865 1.00735 7.13931 0.901013 7.34668 0.900879Z"
        fill="black"
        fillOpacity="0.45"
      />
    </g>
    <defs>
      <clipPath id="clip0_411_5895">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const BeNotFondOfOutlined = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_469_1348)">
      <path
        d="M8.66553 15.1001C7.25734 15.1001 6.11501 13.9584 6.11475 12.5503V10.5503H2.81494C2.26022 10.5546 1.73093 10.3155 1.3667 9.89697C1.00118 9.47651 0.838944 8.91659 0.922363 8.36572V8.36475L1.81885 2.51416C1.96112 1.5802 2.76825 0.894365 3.7124 0.901855V0.900879L11.2651 0.899902H13.0005V0.900879C14.0615 0.88747 14.9673 1.6667 15.1099 2.71924C15.1135 2.74584 15.1147 2.77345 15.1147 2.80029V7.3501C15.1147 7.37681 15.1134 7.4037 15.1099 7.43018C14.9729 8.44302 14.1 9.33436 13.0005 9.3208V9.32178H11.6226L9.21338 14.7446L9.17236 14.8218C9.0636 14.9934 8.8727 15.0999 8.66553 15.1001ZM13.0112 8.12158C13.4215 8.12865 13.8369 7.7763 13.9155 7.29736V2.85498C13.8432 2.41504 13.4596 2.09233 13.0112 2.1001H11.8647V8.12158H13.0112ZM10.6655 8.52393V2.1001H3.70557C3.35651 2.09615 3.05769 2.35072 3.00537 2.6958V2.69678L2.10791 8.54639C2.07757 8.74897 2.1376 8.95525 2.27197 9.10986C2.40671 9.26454 2.60303 9.35237 2.80811 9.3501H6.71533C7.04646 9.35038 7.31494 9.61949 7.31494 9.95068V12.5503C7.31516 13.1685 7.73127 13.6893 8.29834 13.8491L10.6655 8.52393Z"
        fill="black"
        fillOpacity="0.45"
      />
    </g>
    <defs>
      <clipPath id="clip0_469_1348">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const BeNotFondOfFilled = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_411_5899)">
      <path
        d="M11.2529 0.900769C11.5843 0.900769 11.8525 1.16901 11.8525 1.50038V8.65077C11.8524 8.73442 11.8347 8.81749 11.8007 8.89393L9.20113 14.7445L9.16012 14.8217C9.05126 14.9932 8.86046 15.0999 8.65328 15.1C7.24524 15.0998 6.10276 13.9582 6.1025 12.5502V10.5502H2.80269C2.24795 10.5543 1.71862 10.3156 1.35445 9.89686C0.988954 9.47642 0.826731 8.91645 0.910116 8.36561V8.36464L1.8066 2.51405C1.94886 1.58019 2.75614 0.894413 3.70015 0.901745V0.900769H11.2529ZM13.1953 0.899792C14.2286 0.878673 14.9881 1.74516 15.1025 2.73085C15.1051 2.75372 15.1064 2.77716 15.1064 2.80018V7.34999C15.1064 7.37299 15.1051 7.39647 15.1025 7.41932C14.9931 8.36124 14.2648 9.34352 13.1953 9.32167C12.8689 9.31494 12.6075 9.04851 12.6074 8.72206V1.4994C12.6077 1.17314 12.869 0.90652 13.1953 0.899792Z"
        fill="black"
        fillOpacity="0.45"
      />
    </g>
    <defs>
      <clipPath id="clip0_411_5899">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

const MessageIcon = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <path
      d="M8.77197 2.53906C9.04522 2.59514 9.25049 2.83712 9.25049 3.12695C9.25049 3.41679 9.04522 3.65877 8.77197 3.71484L8.65088 3.72656H2.82275C2.418 3.72674 2.08953 4.05521 2.08936 4.45996V11.7227C2.08936 12.1276 2.41789 12.4559 2.82275 12.4561H3.79053C4.46915 12.4563 5.01904 13.0069 5.01904 13.6855C5.01908 13.6946 5.02116 13.6978 5.02197 13.6992C5.02353 13.7019 5.02688 13.7063 5.03271 13.71C5.03851 13.7135 5.04417 13.7146 5.04736 13.7148C5.04894 13.7149 5.05358 13.7152 5.06201 13.7109L7.11768 12.666C7.38907 12.5281 7.68921 12.4561 7.99365 12.4561H13.1763C13.5813 12.456 13.9097 12.1277 13.9097 11.7227V8.0918C13.9097 7.76058 14.1781 7.49146 14.5093 7.49121C14.8406 7.49123 15.1099 7.76044 15.1099 8.0918V11.7227C15.1099 12.7904 14.244 13.6562 13.1763 13.6562H7.99365C7.8783 13.6563 7.76447 13.6831 7.66162 13.7354L5.60498 14.7812C4.78745 15.1964 3.81907 14.6025 3.81885 13.6855C3.81885 13.6696 3.80641 13.6565 3.79053 13.6562H2.82275C1.75515 13.6561 0.890137 12.7903 0.890137 11.7227V4.45996C0.890313 3.39247 1.75526 2.52752 2.82275 2.52734H8.65088L8.77197 2.53906Z"
      fill="black"
      fillOpacity="0.45"
    />
    <path
      d="M13.7964 5.33326L13.9364 5.00473C14.1859 4.41895 14.6351 3.95256 15.1958 3.69742L15.6269 3.5012C15.8601 3.3951 15.8601 3.04797 15.6269 2.94187L15.2199 2.75662C14.6448 2.49491 14.1875 2.01126 13.9423 1.40558L13.7986 1.05056C13.6985 0.803133 13.3645 0.803133 13.2644 1.05056L13.1206 1.40558C12.8755 2.01126 12.4182 2.49491 11.8431 2.75662L11.4361 2.94187C11.203 3.04797 11.203 3.3951 11.4361 3.5012L11.8672 3.69742C12.4279 3.95256 12.8772 4.41895 13.1266 5.00473L13.2665 5.33326C13.369 5.57377 13.6941 5.57377 13.7964 5.33326Z"
      fill="black"
      fillOpacity="0.45"
    />
  </svg>
);

const ChatNew = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      d="M5.83337 18.9331H17.9167"
      stroke="#3D68F5"
      strokeWidth="1.62162"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.67542 13.8156V16.2048H8.07681L14.8646 9.41404L12.4672 7.01562L5.67542 13.8156Z"
      stroke="#3D68F5"
      strokeWidth="1.62162"
      strokeLinejoin="round"
    />
    <path
      d="M16.4121 6.24988L16.587 5.83922C16.8988 5.107 17.4604 4.52402 18.1612 4.20508L18.7001 3.95981C18.9916 3.82719 18.9916 3.39327 18.7001 3.26065L18.1914 3.02908C17.4726 2.70195 16.9009 2.09739 16.5944 1.34029L16.4148 0.896515C16.2896 0.587229 15.8722 0.587229 15.7469 0.896515L15.5673 1.34029C15.2609 2.09739 14.6892 2.70195 13.9704 3.02908L13.4616 3.26065C13.1702 3.39327 13.1702 3.82719 13.4616 3.95981L14.0005 4.20508C14.7013 4.52402 15.263 5.107 15.5748 5.83922L15.7496 6.24988C15.8777 6.55052 16.2841 6.55052 16.4121 6.24988Z"
      fill="#9175F8"
    />
    <path
      d="M4.85333 9.05072L5.06161 8.57322C5.43281 7.72179 6.10139 7.0439 6.93567 6.67305L7.57723 6.38785C7.92418 6.23364 7.92418 5.72908 7.57723 5.57487L6.97157 5.30561C6.11583 4.92522 5.43526 4.22224 5.07039 3.3419L4.85654 2.82588C4.70756 2.46624 4.2106 2.46624 4.06153 2.82588L3.84768 3.3419C3.4829 4.22224 2.80232 4.92522 1.94658 5.30561L1.34084 5.57487C0.99397 5.72908 0.99397 6.23364 1.34084 6.38785L1.98248 6.67305C2.81677 7.0439 3.48535 7.72179 3.85655 8.57322L4.06474 9.05072C4.21719 9.4003 4.70097 9.4003 4.85333 9.05072Z"
      fill="#3D68F5"
    />
  </svg>
);

const DeleteChat = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    {...props}
  >
    <g clipPath="url(#clip0_322_9398)">
      <path
        d="M9.23608 0.670898C9.69464 0.671045 10.1187 0.919102 10.3425 1.31934L10.6941 1.94824C10.8186 2.17101 10.8661 2.40743 10.8533 2.63477H14.8132L14.9333 2.64648C15.2067 2.70236 15.4127 2.94452 15.4128 3.23438C15.4128 3.52433 15.2068 3.76632 14.9333 3.82227L14.8132 3.83398H14.0056L13.2849 13.0195C13.1788 14.3724 12.0492 15.416 10.6921 15.416H5.30737C3.95031 15.416 2.82174 14.3724 2.71558 13.0195L1.9939 3.83398H1.18726C0.855885 3.83398 0.587646 3.56575 0.587646 3.23438C0.587822 2.90315 0.855994 2.63477 1.18726 2.63477H5.15796C5.14477 2.40383 5.1943 2.164 5.323 1.93848L5.68237 1.30957C5.90781 0.914602 6.32818 0.670898 6.78296 0.670898H9.23608ZM3.91187 12.9258C3.96909 13.6542 4.57669 14.2158 5.30737 14.2158H10.6921C11.4228 14.2158 12.0304 13.6542 12.0876 12.9258L12.8015 3.83398H3.19897L3.91187 12.9258ZM6.29761 6.72266C6.62894 6.7227 6.89722 6.99092 6.89722 7.32227V9.78613C6.89722 10.1175 6.62894 10.3857 6.29761 10.3857C5.96624 10.3857 5.698 10.1175 5.698 9.78613V7.32227C5.698 6.99089 5.96624 6.72266 6.29761 6.72266ZM9.70288 6.72266C10.0341 6.72288 10.3025 6.99103 10.3025 7.32227V9.78613C10.3025 10.1174 10.0341 10.3855 9.70288 10.3857C9.37151 10.3857 9.10327 10.1175 9.10327 9.78613V7.32227C9.10327 6.99089 9.37151 6.72266 9.70288 6.72266ZM6.78296 1.87012C6.75902 1.87012 6.73623 1.88351 6.72437 1.9043L6.36499 2.53418C6.35667 2.54897 6.35506 2.55981 6.35522 2.56738C6.35558 2.57674 6.35921 2.58895 6.36597 2.60059C6.37275 2.61195 6.38155 2.62022 6.3894 2.625C6.396 2.62889 6.40657 2.63379 6.42358 2.63379H9.58862C9.60552 2.63371 9.61526 2.62889 9.62183 2.625C9.6299 2.62016 9.63934 2.61229 9.64624 2.60059C9.65311 2.58887 9.6557 2.57681 9.65601 2.56738C9.65621 2.55978 9.65534 2.54893 9.64722 2.53418L9.29468 1.9043C9.28292 1.88351 9.25997 1.87027 9.23608 1.87012H6.78296Z"
        fill="currentColor"
      />
    </g>
    <defs>
      <clipPath id="clip0_322_9398">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export {
  AIFilled,
  ArrowUpFilled,
  SendFilled,
  ArrowDowmOutlined,
  ArrowUpOutlined,
  CopyIcon,
  RefreshIcon,
  BeFondOfOutlined,
  BeFondOfFilled,
  BeNotFondOfOutlined,
  BeNotFondOfFilled,
  MessageIcon,
  ChatNew,
  DeleteChat,
};
