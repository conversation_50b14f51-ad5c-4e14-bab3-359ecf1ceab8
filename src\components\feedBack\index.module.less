.feedBack {
  position: absolute;
  cursor: pointer;
  right: 18px;
  bottom: 34px;
  display: inline-flex;
  gap: 6px;
  padding: 0 13px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  position: relative;

  border-radius: 77px;
  outline: 1px solid #e8ebed;
  box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);

  &:hover,
  &:active {
    background: var(---, rgba(0, 0, 0, 0.06));
    box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);
  }

  .text,
  .enText {
    width: 16px;
    color: rgba(0, 0, 0, 0.88);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }
  .enText {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    height: fit-content;
    width: fit-content;
  }

  .modal {
    position: absolute;
    right: 100%;
    bottom: 0;
    width: 400px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
    border: 1px solid #e8ebed;
    padding: 24px;
    margin-right: 12px;
    z-index: 1000;
    transition: all 0.3s ease;
  }

  .modalShow {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
  }

  .modalHide {
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
  }

  .title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.88);
    margin-bottom: 8px;
  }

  .subDesc {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 20px;
  }

  .ratingSection {
    margin-bottom: 20px;
  }

  .ratingLabel {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.88);
    margin-bottom: 12px;
    font-weight: 500;
  }

  .required {
    color: #ff4d4f;
  }

  .ratingButtons {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .ratingButton {
    width: 32px;
    height: 32px;
    border: 1px solid #d9d9d9;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      border-color: #1890ff;
      color: #1890ff;
    }
  }

  .ratingButtonActive {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
      color: white;
    }
  }

  .suggestionSection {
    margin-bottom: 24px;
  }

  .suggestionLabel {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.88);
    margin-bottom: 8px;
    font-weight: 500;
  }

  .suggestionTextarea {
    width: 100%;
    min-height: 80px;
    padding: 8px 12px;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.88);
    resize: vertical;
    outline: none;
    transition: border-color 0.2s ease;

    &:focus {
      border-color: #1890ff;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
    }

    &::placeholder {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .buttonGroup {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
  }

  .cancelButton,
  .submitButton {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 1px solid;
  }

  .cancelButton {
    background: white;
    border-color: #d9d9d9;
    color: rgba(0, 0, 0, 0.65);

    &:hover {
      border-color: #40a9ff;
      color: #40a9ff;
    }
  }

  .submitButton {
    background: #1890ff;
    border-color: #1890ff;
    color: white;

    &:hover {
      background: #40a9ff;
      border-color: #40a9ff;
    }
  }

  .thankYou {
    text-align: center;
    padding: 40px 20px;
  }

  .thankYouText {
    font-size: 16px;
    color: #52c41a;
    font-weight: 500;
  }
}
