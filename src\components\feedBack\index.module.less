.feedBack {
  position: absolute;
  cursor: pointer;
  right: 18px;
  bottom: 34px;
  display: inline-flex;
  gap: 6px;
  padding: 0 13px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
//   position: relative;

  border-radius: 77px;
  outline: 1px solid #e8ebed;
  box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);

  &:hover,
  &:active {
    background: var(---, rgba(0, 0, 0, 0.06));
    box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);
  }

  .text,
  .enText {
    width: 16px;
    color: rgba(0, 0, 0, 0.88);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }
  .enText {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    height: fit-content;
    width: fit-content;
  }

  .onModal {
    position: absolute;
    left: 100%;
    bottom: 0;
    display: flex;
    width: 520px;
    flex-direction: column;
    align-items: center;
  }
}
