.feedBack {
  position: absolute;
  cursor: pointer;
  right: 18px;
  bottom: 34px;
  display: inline-flex;
  gap: 6px;
  padding: 0 13px;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  border-radius: 77px;
  outline: 1px solid #e8ebed;
  box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);

  &:hover,
  &:active {
    background: var(---, rgba(0, 0, 0, 0.06));
    box-shadow: 0 18px 36px 0 rgba(0, 0, 0, 0.08);
  }

  .text,
  .enText {
    width: 16px;
    color: rgba(0, 0, 0, 0.88);
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    line-height: 18px;
  }
  .enText {
    writing-mode: vertical-rl;
    text-orientation: mixed;
    height: fit-content;
    width: fit-content;
  }
}

.feedbackDropdown {
  background: #fff;
  border-radius: 8px;
  box-shadow:
    0px 9px 28px 8px rgba(0, 0, 0, 0.05),
    0px 3px 6px -4px rgba(0, 0, 0, 0.12),
    0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  width: 400px;
  padding: 24px;
  overflow: hidden;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.88);
  margin-bottom: 8px;
}

.subDesc {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 20px;
}

.ratingSection {
  margin-bottom: 20px;
}

.ratingLabel {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  margin-bottom: 12px;
  font-weight: 500;
}

.required {
  color: #ff4d4f;
}

.ratingButtons {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.ratingButton {
  width: 32px;
  height: 32px;
  border: 1px solid #d9d9d9;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.ratingButtonActive {
  background: #1890ff;
  border-color: #1890ff;
  color: white;

  &:hover {
    background: #40a9ff;
    border-color: #40a9ff;
    color: white;
  }
}

.suggestionSection {
  margin-bottom: 24px;
}

.suggestionLabel {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  margin-bottom: 8px;
  font-weight: 500;
}

.suggestionTextarea {
  width: 100%;
  min-height: 80px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.88);
  resize: vertical;
  outline: none;
  transition: border-color 0.2s ease;

  &:focus {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
  }

  &::placeholder {
    color: rgba(0, 0, 0, 0.45);
  }
}

.buttonGroup {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.cancelButton,
.submitButton {
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.cancelButton {
  background: white;
  border-color: #d9d9d9;
  color: rgba(0, 0, 0, 0.65);

  &:hover {
    border-color: #40a9ff;
    color: #40a9ff;
  }
}

.submitButton {
  background: #1890ff;
  border-color: #1890ff;
  color: white;

  &:hover {
    background: #40a9ff;
    border-color: #40a9ff;
  }
}

.thankYou {
  text-align: center;
  padding: 40px 20px;
}

.thankYouText {
  font-size: 16px;
  color: #52c41a;
  font-weight: 500;
}
