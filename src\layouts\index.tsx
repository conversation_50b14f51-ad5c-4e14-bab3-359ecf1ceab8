import { Outlet, FormattedMessage, setLocale, connect, history } from "umi";
import React, { Component } from "react";
import styles from "./index.less";
import type { MenuProps } from 'antd';
import { Dropdown, Space } from 'antd';
import ch2en from "../assets/ch2en.png";
import en2ch from "../assets/en2ch.png";
import Logo from "../assets/Logo.png";
import defaultAvatar from "../assets/defaultAvatar.png";
const _ = require('lodash');
import Cookies from 'js-cookie';


@connect(({ global }) => ({
  global,
}))
class Layout extends Component {
  constructor(props: {}) {
    super(props);
    this.changeSiderOpenStatus = this.changeSiderOpenStatus.bind(this);
    this.changeLevitateSiderOpenStatus =
    this.changeLevitateSiderOpenStatus.bind(this);
    this.changeLocale = this.changeLocale.bind(this);
    this.handleLogout = this.handleLogout.bind(this);
    this.refreshTokenTimer = null;
  }


  componentDidMount(): void {
    // 
    const defaultLanguage = navigator.language;
    // console.log('Default Language:', defaultLanguage, defaultLanguage.indexOf('zh'));
    if (defaultLanguage.indexOf('zh') <= -1 && !localStorage.umi_locale) {
      setLocale("en_US");
      Cookies.set('locale', 'en')
      this.props.dispatch({
        type: "global/changeLocale",
        targetLocale: 'en_US',
      });
    }

    this.props.dispatch({
      type: "global/getAuthInfo",
    });

    // 设置续期定时器，每一个半小时续期一次
    this.refreshTokenTimer = setInterval(() => {
      this.props.dispatch({
        type: "global/getToken",
      });
    }, 5400000)
  }
  componentWillUnmount(): void {
    clearInterval(this.refreshTokenTimer)
  }

  changeSiderOpenStatus() {
    const { isSiderOpen } = this.props.global;
    this.props.dispatch({
      type: "global/changeSiderOpenStatus",
      isSiderOpen,
    });
  }

  changeLevitateSiderOpenStatus(isOpen: boolean) {
    const { isLevitateSiderOpen } = this.props.global;
    this.props.dispatch({
      type: "global/changeLevitateSiderOpenStatus",
      isOpen,
    });
  }

  handleLogout() {
    this.props.dispatch({
      type: "global/logout",
    });
  }

  changeLocale() {
    const { locale } = this.props.global;
    console.log(locale);
    let targetLocale = "";
    if (locale === "zh_CN") {
      setLocale("en_US");
      Cookies.set('locale', 'en')
      targetLocale = "en_US";
    } else {
      setLocale("zh_CN");
      targetLocale = "zh_CN";
      Cookies.set('locale', 'zh_CN')
    }
    this.props.dispatch({
      type: "global/changeLocale",
      targetLocale,
    });
  }

  render() {
    const {
      changeSiderOpenStatus,
      changeLevitateSiderOpenStatus,
      changeLocale,
      handleLogout
    } = this;
    const sysItems: MenuProps['items'] = [
      {
        key: 'logout',
        label: (
          <a target="_blank" onClick={handleLogout}>
            <FormattedMessage id="logout" />
          </a>
        ),
      },
    ]
    const { isSiderOpen, isLevitateSiderOpen, locale, authInfo } = this.props.global;
    const authAcess = _.includes(authInfo.permissions, 'AI:VIEW') 
    if (authInfo.permissions && !authAcess) history.push('/403')
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <div className={styles.headerLeft} onClick={() => {location.href = '/'}}>
            <img src={Logo} alt="" />
            <div className={styles.title}>腾道AI</div>
          </div>
          <div className={styles.headerRight}>
            <div className={styles.chEnChangeBox} onClick={changeLocale}>
              {locale === "zh_CN" && <img src={ch2en} alt="ch2en" />}
              {locale === "en_US" && <img src={en2ch} alt="en2ch" />}
            </div>
            <Dropdown menu={{ items: sysItems }} placement="bottomRight">
              <Space>
                <div className={styles.avatareBox}>
                  <img src={defaultAvatar} alt="defaultAvatar" />
                </div>
              </Space>
            </Dropdown>
          </div>
        </div>
        {/* <div className={styles.content}>
          <div className={`${styles.contentLeft} ${!isSiderOpen ? styles.contentLeftCollapsed : ''}`}>
          </div>

          <div className={styles.contentRight} style={{marginLeft: isSiderOpen ? 0 : 12}}>
            {
              !isSiderOpen && <div onMouseLeave={() => changeLevitateSiderOpenStatus(false)}>
                <div 
                  className={`${styles.contentLeftCollapsedHotZone}` } 
                  onMouseEnter={() => changeLevitateSiderOpenStatus(true)}
                >
                  <div className={styles.contentLeftCollapsedHotZoneContent} >
                      <div className={styles.openBtn} onClick={changeSiderOpenStatus}>
                        <img src={openBtn} alt="openBtn" />
                      </div>
                  </div>
                </div>
                <div className={`${styles.levitateContentLeft} ${isLevitateSiderOpen ? styles.levitateContentLeftOpen : ''}`}>
                </div>
              </div>
            }
            <div className={styles.chatContainer}>
              <Outlet />
            </div>
          </div>
        </div> */}
        <div className={styles.chatContainer}>
          <Outlet />
        </div>
      </div>
    );
  }
}

export default Layout;
