// 国家旗帜组件
import React from 'react';
import styles from './index.module.less';
import config from '@/services/hostConfig';

// 获取当前环境
const env = process.env.UMI_APP_ENV || 'prod';

const getFlag = (code) => {
  const url = `https://static.${config[env].location}/site/3.0.0/flag/${code}.svg`;
  console.log('location信息=========================>', env,  process.env.UMI_APP_ENV, url)
  
  return url;
};

const CountryFlag = ({ style, code }) => {
  const getFlags = (codes) => {
    if (codes === 'N' || !codes) return '';
    const img = getFlag(codes) || '';
    return img ? <img className={styles.flagImg} style={{ ...style }} src={img} onError={() => ''} /> : '';
  };
  return getFlags(code);
};

export default CountryFlag;
