/* ChatMessage 样式 */
.messageContainer {
  display: flex;
  gap: 12px;
  margin-bottom: 40px;
  animation: messageEnter 0.3s ease-out;
}

/* AI消息 - 左对齐 */
.assistantMessage {
  justify-content: flex-start;
  flex-direction: row;
}

/* 用户消息 - 右对齐 */
.userMessage {
  justify-content: flex-end !important;
  flex-direction: row !important;

  /* 确保用户消息容器右对齐 */
  .messageContent {
    margin-left: auto !important;
    margin-right: 0 !important;
  }
}

@keyframes messageEnter {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* AI消息内容 */
.assistantContent {
  flex: 1;
  max-width: calc(100% - 56px);
  min-width: 0; /* 确保flex子元素可以收缩 */
  /* 强制内容换行 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  overflow: hidden;
}

/* 用户消息内容 */
.userContent {
  max-width: 70% !important;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-end !important;
  margin-left: auto !important;
  margin-right: 0 !important;
}

.messageBubble {
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  white-space: pre-wrap;
  /* 确保内容不会超出容器 */
  overflow: hidden;

  &.user {
    background: #e3edff;
    color: #1e293b;
    border-radius: 12px 0px 12px 12px;
    padding: 12px 16px;
    margin-left: auto;
  }

  &.assistant {
    /* 确保AI消息内容正确换行 */
    width: 100%;
    // color: #1e293b;
    // background: #f8fafc;
    // border-radius: 16px;
    // border-bottom-left-radius: 4px;
    // padding: 12px 16px;
  }
}

/* AI扩展信息样式 */
.aiExtras {
  margin-top: 8px;
}

.thoughtsSection {
  margin-bottom: 12px;
  opacity: 1;
  transition: opacity 0.3s ease;

  &.thinking {
    opacity: 0.7;
  }
}

.thoughtsToggle {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.04);

  padding: 4px 16px;
  border-radius: 6px;
  color: rgba(0, 0, 0, 0.65);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;

  line-height: 24px;
}

.thoughtsContent {
  margin-top: 8px;
  padding: 0 16px;
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
  color: rgba(0, 0, 0, 0.45);
  border-left: 2px solid #e8ebed;

  p {
    margin: 0.5em 0;
  }

  ul,
  ol {
    margin: 0.5em 0;
    padding-left: 1.5em;
  }
}

.thoughtItem {
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;

  &:last-child {
    border-bottom: none;
  }

  p {
    margin: 0;
  }
}

.messageUserText {
  font-size: 14px;
  line-height: 24px;
  width: 100%;
  max-width: 100%;
  opacity: 1;
  transition: opacity 0.1s ease-in-out;
  /* 强制换行设置 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  white-space: pre-wrap;
  overflow: hidden;
}

.messageText {
  width: 100%;
  max-width: 100%;
  opacity: 1;
  transition: opacity 0.1s ease-in-out;
  /* 强制换行设置 */
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  white-space: pre-wrap;
  overflow: hidden;

  &.typing {
    opacity: 0.7;
  }

  p {
    margin: 0;
    min-height: 24px;
    /* 确保段落内容正确换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    white-space: pre-wrap;

    + p {
      margin-top: 12px;
    }
  }

  /* Markdown 样式 */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    color: #1e293b;
    font-weight: 600;
    line-height: 1.4;
    /* 标题换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  ul,
  ol {
    color: #1e293b;
    /* 列表换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  li {
    margin: 0.25em 0;
    /* 列表项换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  blockquote {
    border-left: 4px solid #e2e8f0;
    padding-left: 16px;
    margin: 12px 0;
    color: #64748b;
    font-style: italic;
  }

  table {
    border-collapse: collapse;
    width: 100%;
    margin: 12px 0;
    /* 表格内容换行 */
    table-layout: fixed;
    word-wrap: break-word;
  }

  th,
  td {
    border: 1px solid #e2e8f0;
    padding: 8px 12px;
    text-align: left;
    /* 表格单元格内容换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
  }

  th {
    background-color: #f8fafc;
    font-weight: 600;
  }

  code {
    background-color: #f1f5f9;
    color: #e11d48;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.9em;
    /* 内联代码换行 */
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-all;
  }

  pre {
    background-color: #1e293b;
    color: #f8fafc;
    padding: 16px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 12px 0;
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 0.9em;
    line-height: 1.5;
    /* 代码块换行 */
    white-space: pre-wrap;
    word-wrap: break-word;
    overflow-wrap: break-word;

    code {
      background: none;
      color: inherit;
      padding: 0;
      border-radius: 0;
      font-size: inherit;
      /* 代码块内代码换行 */
      white-space: pre-wrap;
      word-wrap: break-word;
      overflow-wrap: break-word;
    }
  }

  a {
    color: #3b82f6;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }

  hr {
    border: none;
    border-top: 1px solid #e2e8f0;
    margin: 16px 0;
  }
}

/* 消息操作按钮 */
.messageActions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.line {
  width: 1px;
  height: 12px;
  border-left: 1px solid #e8ebed;
}

.actionButton {
  padding: 4px;
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  line-height: 1;

  &:hover {
    background: rgba(0, 0, 0, 0.06);
    border-radius: 4px;
  }

  svg {
    width: 16px;
    height: 16px;
    display: block;
  }
}

/* 打字指示器 */
.typingIndicator {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
}

.typingDot {
  width: 6px;
  height: 6px;
  background-color: #94a3b8;
  border-radius: 50%;
  animation: blink 1s infinite;
  animation-delay: var(--delay);
}

// 定制化按钮样式
.customActionContainer {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;

  .customActionButton {
    border-radius: 6px;
    background: rgba(0, 0, 0, 0.04);
    color: rgba(0, 0, 0, 0.65);
    font-size: 14px;
    font-weight: 400;
    height: 32px;
    display: inline-flex;
    align-items: center;
    padding: 0 16px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.06);
    }
  }
}

// 企业链接样式
.messageText {
  :global(a) {
    color: #3D68F5;
    text-decoration: none !important;
    word-wrap: break-word;
    overflow-wrap: break-word;
    transition: color 0.2s ease;

    &:hover {
      color: #5B7EF7;
      text-decoration: none !important;
    }

    &:visited {
      color: #3D68F5;
      text-decoration: none !important;
    }

    &:focus {
      text-decoration: none !important;
      outline: none;
    }
  }
}
