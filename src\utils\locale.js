/**
 * 语言工具函数
 */

/**
 * 获取当前语言设置
 * @returns {string} 'zh_CN' | 'en_US'
 */
export const getCurrentLocale = () => {
  try {
    const locale = localStorage.getItem('umi_locale');
    return locale || 'zh_CN'; // 默认中文
  } catch (error) {
    console.warn('Failed to get locale from localStorage:', error);
    return 'zh_CN'; // 默认中文
  }
};

/**
 * 判断当前是否为中文环境
 * @returns {boolean}
 */
export const isCN = getCurrentLocale() === 'zh_CN';
