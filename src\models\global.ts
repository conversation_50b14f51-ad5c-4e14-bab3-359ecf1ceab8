import { getAuth, logout } from '@/services/global';
import { message } from 'antd';
const appEnv = process.env.UMI_APP_ENV || 'dev';

export default {
  state: {
    isSiderOpen: true,
    isLevitateSiderOpen: false,
    locale: localStorage.umi_locale || 'zh_CN',
    authInfo : {}
  },

  effects: {
    *getAuthInfo({ payload }, { call, put }) {
      const res  = yield call(getAuth, payload);
      if (res && res.userId) {
        yield put({ type: 'queryAuthInfo', payload: res });
      } else {
        console.log('获取权限信息失败...')
      }
    },
    *logout({ call }) {
      console.log('logout!')
      const res  = yield call(logout);
      if (res) {
        let host = 'https://login.tendata.net/login'
        if (appEnv === 'prod') host = 'https://login.tendata.cn/login';
        window.location.href = host;
      } else {
        message.error('退出的登录失败')
      }
    },
  },

  reducers: {
    queryAuthInfo(state: any, { payload }: any) {
      localStorage.authInfo = JSON.stringify(payload);
      return {
        ...state,
        authInfo: payload,
      };
    },
    changeSiderOpenStatus(state: { isSiderOpen: boolean }) {
      const { isSiderOpen } = state;
      return {
        ...state,
        isSiderOpen: !isSiderOpen,
        isLevitateSiderOpen: false
      };
    },
    changeLevitateSiderOpenStatus(state: any, payload: { isOpen: boolean }) {
      const { isOpen } = payload;
      return {
        ...state,
        isLevitateSiderOpen: isOpen,
      };
    },
    changeLocale(state: any, payload: { targetLocale: string }) {
      const { targetLocale } = payload;
      return {
        ...state,
        locale: targetLocale,
      };
    },
  },
};