import { getAuth, logout, getToken } from '@/services/global';
import { message } from 'antd';
import Cookies from 'js-cookie';
const appEnv = process.env.UMI_APP_ENV || 'dev';
const LOGIN_HOST = `https://login.tendata.${appEnv === 'prod' ? 'cn' : 'net'}/login`

export default {
  state: {
    isSiderOpen: true,
    isLevitateSiderOpen: false,
    locale: localStorage.umi_locale || 'zh_CN',
    authInfo : {}
  },

  effects: {
    *getAuthInfo({ payload }, { call, put }) {
      const res = yield call(getAuth, payload);
      if (res && res.userId) {
        yield put({ type: 'queryAuthInfo', payload: res });
      } else {
        console.log('获取权限信息失败...')
      }
    },
    *logout({ payload }, { call, put }) {
      const res  = yield call(logout);
      if (res) {
        window.location.href = LOGIN_HOST;
      } else {
        message.error('退出的登录失败')
      }
    },
    *getToken({ payload }, { call, put }) {
      const res = yield call(getToken, payload);
      if (res && res.access_token) {
        Cookies.set("refresh_token", res.refresh_token);
        Cookies.set("token", res.access_token);
        const now = new Date().toISOString();
        Cookies.set("tokenUpdateTimestamp", now,);
      } else {
        if (appEnv !== 'dev') window.location.href = LOGIN_HOST;
      }
    },
  },

  reducers: {
    queryAuthInfo(state: any, { payload }: any) {
      localStorage.authInfo = JSON.stringify(payload);
      return {
        ...state,
        authInfo: payload,
      };
    },
    changeSiderOpenStatus(state: { isSiderOpen: boolean }) {
      const { isSiderOpen } = state;
      return {
        ...state,
        isSiderOpen: !isSiderOpen,
        isLevitateSiderOpen: false
      };
    },
    changeLevitateSiderOpenStatus(state: any, payload: { isOpen: boolean }) {
      const { isOpen } = payload;
      return {
        ...state,
        isLevitateSiderOpen: isOpen,
      };
    },
    changeLocale(state: any, payload: { targetLocale: string }) {
      const { targetLocale } = payload;
      return {
        ...state,
        locale: targetLocale,
      };
    },
  },
};