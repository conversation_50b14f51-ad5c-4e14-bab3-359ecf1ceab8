.chatPage {
  height: calc(100vh - 70px);
  display: flex;
}

.contentRight {
  flex: 1;
  border-radius: 12px;
  border: 1px solid var(---, #eaeaea);
  background: var(---W, #fff);
  margin-right: 12px;
  min-width: 720px;
  .topLeft {
    display: flex;
    align-items: center;
    position: relative;
    height: 0; /* 不占用高度 */
  }
}

.hideBox {
  position: absolute;
  left: 0;
  top: 14px;
  z-index: 10;

  display: flex;
  align-items: center;

  .addChat {
    margin-left: 8px;
    padding: 3px 12px;
    border-radius: 16px;
    border: 1px solid rgba(61, 104, 245, 0.4);
    background: rgba(69, 110, 245, 0.04);
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    color: #3d68f5;
  }

  .line {
    width: 1px;
    height: 20px;
    border-left: 1px solid #e8ebed;
    margin: 0 12px;
  }

  .modelName {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    padding: 3px 12px;
    border-radius: 16px;
    border: 1px solid #eaeaea;
    font-size: 12px;
    font-weight: 400;
    line-height: 20px;
  }
}

.hoverArea {
  position: relative;
}

.hoverAreaContent {
  position: absolute;
  top: 0;
  left: -12px;
  width: calc(100% + 12px);
  height: calc(100vh - 100px);
  cursor: pointer;
  // background: red;
}

.chatContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chatContainerCollapsed {
  height: calc(100% - 32px);
}

.contentLeftCollapsedHotZone {
  cursor: pointer;
  .openBtn {
    cursor: pointer;
    margin-left: 16px;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      background: rgba(0, 0, 0, 0.04);
    }
    img {
      width: 18px;
      height: 18px;
    }
  }
}
.levitateContentLeft {
  position: absolute;
  display: flex;
  height: 748px;
  top: 50px;
  left: 20px;
  flex-direction: column;
  align-items: flex-start;
  flex-shrink: 0;
  border-radius: 12px;
  border: 1px solid #eaeaea;
  background: #f7f7fc;
  box-shadow: 0px 12px 32px 0px rgba(0, 0, 0, 0.08);
  transform: translateX(-120%) !important;
  transition: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 80;
}
.levitateContentLeftOpen {
  transform: translateX(0) !important;
  transition: 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}
