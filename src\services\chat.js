import { request } from "umi";
import { message } from "antd";
import { isCN } from "@/utils/locale";
import { url } from "./hostConfig";

// const env = process.env.UMI_ENV || process.env.NODE_ENV || 'development';
// console.log(process.env.API_BASE_URL,"==process.env.API_BASE_URL");
// console.log(env,"env");

// 获取会话列表
export async function getChatList(params) {
  const { offset = 0, limit = 50 } = params;
  const response = await request("/api/ai/v1/chats", {
    method: "GET",
    params: {
      offset,
      limit,
    },
  });

  return response?.data || {};
}

// 创建新会话
export async function createChat(params) {
  const response = await request("/api/ai/v1/chats", {
    method: "POST",
    data: params,
  });

  return response?.data || {};
}

// 更新会话
export async function updateChat(id, params) {
  const response = await request(`/api/ai/v1/chats/${id}`, {
    method: "PUT",
    data: params,
  });

  return response?.data || {};
}

// 删除会话
export async function deleteChat(id) {
  const response = await request(`/api/ai/v1/chats/${id}`, {
    method: "DELETE",
  });

  return response?.data || {};
}

// 获取公司列表
export async function getCompanyList(params = {}) {
  const { page = 0, size = 20 } = params;
  return request("/api/bizr/v1/internal/user/company", {
    method: "GET",
    params: {
      page,
      size,
    },
  });
}

// 获取会话消息列表
export async function getChatMessages(chatId, params) {
  const { offset = 0, limit = 50 } = params;
  const response = await request("/api/ai/v1/messages", {
    method: "GET",
    params: {
      chatId,
      offset,
      limit,
    },
  });

  return response?.data || {};
}

// 消息反馈接口
export async function messageFeedback(messageId, like) {
  const response = await request(`/api/ai/v1/messages/${messageId}/feedback`, {
    method: "POST",
    data: {
      like: like
    },
  });

  return response?.data || {};
}

// 消息SSE
export async function chatService(
  content,
  onChunk,
  signal,
  chatId = "default"
) {
  let reader;
  try {
    // 构造新的请求体格式
    const requestBody = {
      chatId,
      content:
        typeof content === "object" && content !== null && content.content
          ? content.content
          : typeof content === "object" && content !== null
          ? content
          : {
              userPrompt: content,
              entities: [],
            },
    };

    // 如果传入的参数包含scene，添加到请求体中
    if (typeof content === "object" && content !== null && content.scene) {
      requestBody.scene = content.scene;
    }

    const response = await fetch(`/api/ai/v1/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(requestBody),
      signal, // 添加signal以支持取消
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = "";
    let accumulatedThinking = "";
    let accumulatedContent = "";
    let accumulatedTips = [];
    let isThinking = true;

    async function processText() {
      try {
        while (true) {
          const { value, done } = await reader.read();
          if (done) break;

          buffer += decoder.decode(value, { stream: true });

          const lines = buffer.split("\n");
          buffer = lines.pop() || "";

          for (const line of lines) {
            if (!line.trim() || !line.startsWith("data:")) continue;

            try {
              // 处理 "data:" 或 "data: " 两种格式
              const jsonStr = line.startsWith("data: ") ? line.slice(6) : line.slice(5);
              const data = JSON.parse(jsonStr);

              // 检查是否是错误响应 - 只要有code字段就认为是错误
              if (data.hasOwnProperty('code')) {
                // 特殊处理 CHAT_IN_PROGRESS_MAX 错误码
                if (data.code === "CHAT_IN_PROGRESS_MAX") {
                  console.log(9090);
                  
                  const errorMessage = isCN
                    ? "已有三个提问正在回答，在其中一个完成后再提问吧"
                    : "There are already three questions in progress. Please start a new question after one has ended.";
                  message.error(errorMessage);
                }
                throw new Error(`SSE Error: ${data.code}`);
              }

              // 处理新的数据格式：{"content":null,"thinkingContent":"We","tips":null}
              if (data.hasOwnProperty('thinkingContent') || data.hasOwnProperty('content') || data.hasOwnProperty('tips')) {
                if (data.thinkingContent !== null && data.thinkingContent !== undefined) {
                  // 思考内容
                  accumulatedThinking += data.thinkingContent;
                  onChunk({ type: "thought", content: data.thinkingContent });
                } else if (data.content !== null && data.content !== undefined) {
                  // 主要内容
                  if (isThinking) {
                    // 从思考阶段切换到内容阶段
                    isThinking = false;
                    onChunk({ type: "separator" });
                  }
                  accumulatedContent += data.content;
                  onChunk({ type: "text", content: data.content });
                }

                // 处理 tips 字段
                if (data.tips !== null && data.tips !== undefined && Array.isArray(data.tips)) {
                  accumulatedTips = data.tips;
                  onChunk({ type: "tips", content: data.tips });
                }
              } else {
                // 兼容旧格式：带有 type 字段的格式
                switch (data.type) {
                  case "start":
                    // 开始接收数据
                    console.log("Stream started");
                    break;
                  case "thinking":
                    // 思考过程
                    if (data.thinkingContent) {
                      accumulatedThinking += data.thinkingContent;
                      onChunk({ type: "thought", content: data.thinkingContent });
                    }
                    break;
                  case "thinking_complete":
                    // 思考完成，切换到回复阶段
                    isThinking = false;
                    onChunk({ type: "separator" });
                    break;
                  case "content":
                    // 主要内容
                    if (data.content) {
                      accumulatedContent += data.content;
                      onChunk({ type: "text", content: data.content });
                    }
                    break;
                  case "complete":
                    // 数据接收完成
                    console.log("Stream completed");
                    return {
                      thoughts: data.thinkingContent || accumulatedThinking,
                      content: data.content || accumulatedContent,
                      tips: data.tips || accumulatedTips,
                    };
                }
              }
            } catch (e) {
              console.error("Error parsing SSE message:", e, "Line:", line);
            }
          }
        }
        return {
          thoughts: accumulatedThinking,
          content: accumulatedContent,
          tips: accumulatedTips,
        };
      } catch (error) {
        if (error.name === "AbortError") {
          // 不需要在这里取消reader，因为fetch已经被取消了
          throw error;
        }
        throw error;
      }
    }

    return await processText();
  } catch (error) {
    if (error.name === "AbortError") {
      // 确保在取消时关闭reader
      if (reader) {
        try {
          await reader.cancel();
        } catch (e) {
          // 忽略reader.cancel()可能产生的错误
        }
      }
      throw error;
    }
    console.error("Chat API Error:", error);
    throw error;
  }
}
