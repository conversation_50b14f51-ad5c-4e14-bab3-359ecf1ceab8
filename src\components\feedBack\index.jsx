import React, { useState } from "react";
import { Dropdown } from "antd";
import { isCN } from "@/utils/locale";
import { FeedBackIcon, CloseIcon } from "@/components/icon";
import { FormattedMessage } from "umi";
import styles from "./index.module.less";

const FeedBack = () => {
  const [open, setOpen] = useState(false);
  const [rating, setRating] = useState(0);
  const [suggestion, setSuggestion] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = () => {
    if (rating === 0) {
      alert("请选择评分");
      return;
    }

    // 这里可以添加提交到后端的逻辑
    console.log("提交反馈:", { rating, suggestion });

    setIsSubmitted(true);

    // 3秒后自动关闭
    setTimeout(() => {
      setOpen(false);
      setIsSubmitted(false);
      setRating(0);
      setSuggestion("");
    }, 3000);
  };

  const handleCancel = () => {
    setOpen(false);
    setRating(0);
    setSuggestion("");
    setIsSubmitted(false);
  };

  // 自定义下拉内容
  const popupRender = () => (
    <div className={styles.feedbackDropdown}>
      <CloseIcon
        className={styles.closeIcon}
        onClick={handleCancel}
      />
      {isSubmitted ? (
        <div className={styles.thankYou}>
          <div className={styles.thankYouText}>感谢您的反馈！</div>
        </div>
      ) : (
        <>
          <div className={styles.title}>给 tendata AI 提反馈建议</div>
          <div className={styles.subDesc}>有你的建议我们可以做得更好</div>

          <div className={styles.ratingSection}>
            <div className={styles.ratingLabel}>评分 <span className={styles.required}>*</span></div>
            <div className={styles.ratingButtons}>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((score) => (
                <button
                  key={score}
                  className={`${styles.ratingButton} ${rating === score ? styles.ratingButtonActive : ''}`}
                  onClick={() => setRating(score)}
                >
                  {score}
                </button>
              ))}
            </div>
          </div>

          <div className={styles.suggestionSection}>
            <div className={styles.suggestionLabel}>反馈建议</div>
            <textarea
              className={styles.suggestionTextarea}
              placeholder="请输入您的建议..."
              value={suggestion}
              onChange={(e) => setSuggestion(e.target.value)}
              rows={4}
            />
          </div>

          <div className={styles.buttonGroup}>
            <button className={styles.cancelButton} onClick={handleCancel}>
              取消
            </button>
            <button className={styles.submitButton} onClick={handleSubmit}>
              提交
            </button>
          </div>
        </>
      )}
    </div>
  );

  return (
    <Dropdown
      open={open}
      onOpenChange={setOpen}
      popupRender={popupRender}
      trigger={["click"]}
      placement="leftBottom"
    >
      <div className={styles.feedBack} style={{ height: isCN ? 86 : 111 }}>
        <FeedBackIcon />
        <span className={isCN ? styles.text : styles.enText}>
          <FormattedMessage id="feedback" />
        </span>
      </div>
    </Dropdown>
  );
};

export default FeedBack;
