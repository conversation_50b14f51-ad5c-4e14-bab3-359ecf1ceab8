import React, { useState } from "react";
import { isCN } from "@/utils/locale";
import { FeedBackIcon } from "@/components/icon";
import { FormattedMessage } from "umi";
import styles from "./index.module.less";

const FeedBack = () => {
  const [submittal, setSubmittal] = useState(false);

  const Modal = () => (
    <div className={submittal ? styles.inModal : styles.onModal}>
      <div className={styles.title}>给 tendata AI 提反馈建议</div>
      <div className={styles.subDesc}>有你的建议我们可以做得更好</div>
    </div>
  );

  return (
    <div className={styles.feedBack} style={{ height: isCN ? 86 : 111 }}>
      <FeedBackIcon />
      <span className={isCN ? styles.text : styles.enText}>
        <FormattedMessage id="feedback" />
      </span>
      <Modal />
    </div>
  );
};

export default FeedBack;
