type RouteConfig = {
  path: string;
  component?: string;
  redirect?: string;
  wrappers?:string,
  layout?:string,
};

export const routesConfig: RouteConfig[] =  [
  { path: '/login', component: 'errorPages/403' },
  {
    path: '/',
    redirect: '/chat',
  },
  {
    path: '/chat', 
    component: '@/pages/chat',
    routes: [
      { path: '/chat/:id', component: '@/pages/chat' },
    ],
    onEnter: async (params, app) => {
      const { authInfo } = app.model.global; // 假设你在 global model 中维护了用户信息
      console.log(app, authInfo, '[}{}{}{}{}{}{}{}{OPOPOPOP')
      // if (!currentUser || !currentUser.isAdmin) {
      //   throw new Error('No permission'); // 抛出错误以阻止路由跳转
      // }
    },
  },
  {path: '/403', component: 'errorPages/403'},
  {path: '/*', component: 'errorPages/404'},
]